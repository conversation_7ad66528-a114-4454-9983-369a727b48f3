PLATFORM=$(shell uname -m)
DATETIME=$(shell date "+%Y%m%d%H%M%S")
export GOPRIVATE=git.woa.com
chain_service:
	@CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build -o bin/chain_service src/main.go
	@rm -rf ./tmp/chain_service/
	@mkdir -p ./tmp/chain_service/
	@mkdir ./tmp/chain_service/bin
	@mkdir ./tmp/chain_service/config
	@mkdir ./tmp/chain_service/log
	@cp bin/restart_test.sh ./tmp/chain_service/bin
	@cp bin/restart_prod.sh ./tmp/chain_service/bin
	@cp bin/chain_service ./tmp/chain_service/bin
	@cp -r config ./tmp/chain_service/
	@cd ./tmp;tar -zcvf chain_service.$(DATETIME).$(PLATFORM).tar.gz chain_service; mv chain_service.$(DATETIME).$(PLATFORM).tar.gz ..
	@rm -rf ./tmp/
	@mkdir -p  ../bin
	@mv *gz ../bin/
test:
	go test -v ./... -coverprofile=cover.out
clean:
	$(RM) tmp/* $(TARGET) 
