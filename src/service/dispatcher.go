package service

import (
	"fmt"
	"sync"
	"time"

	"chainmaker.org/chainmaker/pb-go/v2/common"
	cmSdk "chainmaker.org/chainmaker/sdk-go/v2"
	"git.woa.com/zhixinlian/websdk/seelog"
)

var (
	resultDispatcher       *txResultDispatcher
	defaultInterval        = 200
	defaultTxRegisCapacity = 20000
)

// TxResult tx result
type TxResult struct {
	Result      *common.Result
	TxTimestamp int64
	BlockHeight uint64
}

type txResultDispatcher struct {
	nextBlockNum uint64
	interval     int64 //cm block interval ms
	cc           *cmSdk.ChainClient
	stopC        chan struct{}
	blockC       chan *common.BlockInfo

	mux             sync.RWMutex // mux protect pendingRegistrations
	txMux           sync.Mutex   // mux protect txRegistrations
	txRegistrations map[string]chan *TxResult
	pendingRegister map[string]chan *TxResult
}

// NewTxResultDispatcher tx dis
func NewTxResultDispatcher(cc *cmSdk.ChainClient, interval int64) *txResultDispatcher {
	if resultDispatcher != nil {
		return resultDispatcher
	}

	if interval == 0 {
		interval = int64(defaultInterval)
	}

	chainInfo, err := cc.GetChainInfo()
	if err != nil {
		panic(fmt.Errorf("init chainInfo err[%s]", err.Error()))
	}

	seelog.Debugf("init chain last block[%d]", chainInfo.BlockHeight)
	resultDispatcher = &txResultDispatcher{
		nextBlockNum:    chainInfo.BlockHeight,
		interval:        interval,
		cc:              cc,
		stopC:           make(chan struct{}),
		blockC:          make(chan *common.BlockInfo, 1000),
		txRegistrations: make(map[string]chan *TxResult, defaultTxRegisCapacity),
		pendingRegister: make(map[string]chan *TxResult),
	}

	return resultDispatcher
}

// GetTxResultDispatcher get tx dis
func GetTxResultDispatcher() *txResultDispatcher {
	return resultDispatcher
}

// Start start dis
func (res *txResultDispatcher) Start() {
	go res.loop()
	go res.simSubscribe()
}

// Stop stop dis
func (res *txResultDispatcher) Stop() {
	close(res.stopC)
}

// Register register tx wait tx chain result
func (res *txResultDispatcher) Register(txId string) chan *TxResult {
	startTime := time.Now()
	res.mux.Lock()
	defer res.mux.Unlock()

	if txResultC, exists := res.pendingRegister[txId]; exists {
		seelog.Warnf("tx[%s] already registered, reusing existing channel", txId)
		return txResultC
	}

	txResultC := make(chan *TxResult, 1)
	res.pendingRegister[txId] = txResultC

	lockDuration := time.Since(startTime)
	seelog.Infof("subscribe tx[%s] success, pendingRegister size: %d, lock duration: %v",
		txId, len(res.pendingRegister), lockDuration)

	return txResultC
}

// UnRegister unregister tx
func (res *txResultDispatcher) UnRegister(txId string) {
	startTime := time.Now()
	res.mux.Lock()
	defer res.mux.Unlock()

	if _, exists := res.pendingRegister[txId]; exists {
		delete(res.pendingRegister, txId)
		lockDuration := time.Since(startTime)
		seelog.Infof("unregister tx[%s] from pendingRegister, remaining size: %d, lock duration: %v",
			txId, len(res.pendingRegister), lockDuration)
	} else {
		seelog.Warnf("tx[%s] not found in pendingRegister for unregister", txId)
	}
}

// simSubscribe simulate subscribe cm block
func (res *txResultDispatcher) simSubscribe() {
	timeout := time.Duration(res.interval) * time.Millisecond
	ticker := time.NewTicker(timeout)
	seelog.Infof("simSubscribe started with interval: %dms", res.interval)
	for {
		select {
		case <-ticker.C:
			roundStartTime := time.Now()
			chainInfoStartTime := time.Now()
			chainInfo, err := res.cc.GetChainInfo()
			chainInfoDuration := time.Since(chainInfoStartTime)
			if err != nil {
				seelog.Errorf("GetChainInfo failed: %v, duration: %v", err, chainInfoDuration)
				ticker.Reset(timeout)
				break
			}
			// 慢请求特殊标识日志
			if chainInfoDuration > 100*time.Millisecond {
				seelog.Warnf("GetChainInfo SLOW: GetChainInfo cost too much time, current block height: %d, duration: %v",
					chainInfo.BlockHeight, chainInfoDuration)
			}
			seelog.Debugf("GetChainInfo success, current block height: %d, duration: %v",
				chainInfo.BlockHeight, chainInfoDuration)
			if chainInfo.BlockHeight < res.nextBlockNum {
				seelog.Debugf("no new blocks, current: %d, next expected: %d",
					chainInfo.BlockHeight, res.nextBlockNum)
				ticker.Reset(timeout)
				break
			}
			transferStartTime := time.Now()
			pending := make(map[string]chan *TxResult)
			res.mux.Lock()
			pendingRegister := res.pendingRegister
			res.pendingRegister = pending
			pendingCount := len(pendingRegister)
			res.mux.Unlock()
			res.txMux.Lock()
			startLen := len(res.txRegistrations)
			for txId, resultC := range pendingRegister {
				res.txRegistrations[txId] = resultC
			}
			endLen := len(res.txRegistrations)
			res.txMux.Unlock()
			transferDuration := time.Since(transferStartTime)
			seelog.Infof("state transfer completed: pending=%d, txRegistrations=%d->%d, duration=%v",
				pendingCount, startLen, endLen, transferDuration)
			blocksProcessed := 0
			for chainInfo.BlockHeight >= res.nextBlockNum {
				blockStartTime := time.Now()
				block, err := res.cc.GetBlockByHeight(res.nextBlockNum, false)
				blockDuration := time.Since(blockStartTime)
				if err != nil {
					seelog.Errorf("GetBlockByHeight[%d] failed: %v, duration: %v",
						res.nextBlockNum, err, blockDuration)
					ticker.Reset(timeout)
					break
				}
				seelog.Infof("GetBlockByHeight[%d] success, txs count: %d, duration: %v",
					res.nextBlockNum, len(block.Block.Txs), blockDuration)
				res.nextBlockNum++
				sendStartTime := time.Now()
				select {
				case res.blockC <- block:
					sendDuration := time.Since(sendStartTime)
					seelog.Debugf("block[%d] sent to blockC, duration: %v", res.nextBlockNum-1, sendDuration)
				case <-time.After(1 * time.Second):
					seelog.Errorf("block[%d] send to blockC timeout after 1s", res.nextBlockNum-1)
				}
				blocksProcessed++
			}
			roundDuration := time.Since(roundStartTime)
			seelog.Infof("simSubscribe round completed: blocks processed=%d, total duration=%v",
				blocksProcessed, roundDuration)
			ticker.Reset(timeout)
		case <-res.stopC:
			return
		}
	}
}

// loop handle blockInfo and release blocked transactions.
func (res *txResultDispatcher) loop() {
	seelog.Infof("loop started, waiting for blocks...")

	for {
		select { //循环parse blockInfo unregister
		case blockInfo := <-res.blockC:
			loopStartTime := time.Now()
			blockHeight := blockInfo.Block.Header.BlockHeight
			txCount := len(blockInfo.Block.Txs)

			seelog.Infof("processing block[%d] with %d transactions", blockHeight, txCount)

			res.txMux.Lock()
			registrationsCount := len(res.txRegistrations)
			matchedTxs := 0

			for _, tx := range blockInfo.Block.Txs {
				if txResultC, exists := res.txRegistrations[tx.Payload.TxId]; exists {
					matchedTxs++
					result := &TxResult{
						Result:      tx.Result,
						TxTimestamp: tx.Payload.Timestamp,
						BlockHeight: blockHeight,
					}
					seelog.Infof("tx[%s] completed in block[%d], code[%d]",
						tx.Payload.TxId, blockHeight, tx.Result.Code)

					// non-blocking write to channel to ignore txResultC buffer is full in extreme cases
					select {
					case txResultC <- result:
					default:
						seelog.Warnf("tx[%s] result channel full, dropping result", tx.Payload.TxId)
					}
					delete(res.txRegistrations, tx.Payload.TxId)
					close(txResultC)
				} else {
					seelog.Debugf("tx[%s] not in registrations (external tx)", tx.Payload.TxId)
				}
			}

			remainingRegistrations := len(res.txRegistrations)
			res.txMux.Unlock()

			loopDuration := time.Since(loopStartTime)
			seelog.Infof("block[%d] processed: txs=%d, matched=%d, registrations=%d->%d, duration=%v",
				blockHeight, txCount, matchedTxs, registrationsCount, remainingRegistrations, loopDuration)

		case <-res.stopC:
			seelog.Infof("loop stopped")
			return
		}
	}
}
