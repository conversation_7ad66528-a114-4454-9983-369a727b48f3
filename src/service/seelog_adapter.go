package service

import (
	"fmt"

	"chainmaker.org/chainmaker/sdk-go/v2/utils"
	"git.woa.com/zhixinlian/websdk/seelog"
)

// SeelogAdapter 将seelog适配为SDK的Logger接口
//
// 目的：让ChainMaker SDK使用chain_service项目统一的seelog日志系统，
// 而不是SDK默认的zap logger，实现日志统一管理。
//
// 实现：
// - 实现SDK的utils.Logger接口
// - 将SDK的日志调用转发给seelog系统
// - 保持接口兼容性，不影响SDK的正常使用
type SeelogAdapter struct{}

// NewSeelogAdapter 创建seelog适配器
func NewSeelogAdapter() utils.Logger {
	return &SeelogAdapter{}
}

// Debugf 实现SDK Logger接口
func (s *SeelogAdapter) Debugf(format string, args ...interface{}) {
	seelog.Debugf(format, args...)
}

// Infof 实现SDK Logger接口
func (s *SeelogAdapter) Infof(format string, args ...interface{}) {
	seelog.Infof(format, args...)
}

// Warnf 实现SDK Logger接口
func (s *SeelogAdapter) Warnf(format string, args ...interface{}) {
	seelog.Warnf(format, args...)
}

// Errorf 实现SDK Logger接口
func (s *SeelogAdapter) Errorf(format string, args ...interface{}) {
	seelog.Errorf(format, args...)
}

// Debug 实现SDK Logger接口
func (s *SeelogAdapter) Debug(args ...interface{}) {
	seelog.Debug(fmt.Sprint(args...))
}

// Info 实现SDK Logger接口
func (s *SeelogAdapter) Info(args ...interface{}) {
	seelog.Info(fmt.Sprint(args...))
}

// Warn 实现SDK Logger接口
func (s *SeelogAdapter) Warn(args ...interface{}) {
	seelog.Warn(fmt.Sprint(args...))
}

// Error 实现SDK Logger接口
func (s *SeelogAdapter) Error(args ...interface{}) {
	seelog.Error(fmt.Sprint(args...))
}
