package service

import (
	"chainmaker.org/chainmaker/sdk-go/v2/utils"
	"fmt"
	"testing"
	"time"
)

func TestDispatcher(t *testing.T) {
	mock := NewTxResultDispatcherMock(nil, 0)
	mock.Start()

	go registerTx()
	select {}
}

//每一个周期200ms,构建1000 笔交易
func registerTx() {
	mock := GetTxResultDispatcherMock()
	timeout := time.Duration(200) * time.Millisecond
	ticker := time.NewTicker(timeout)

	for {
		select {
		case <-ticker.C:
			for i := 0; i < 200;i++ {
				txId := utils.GetTimestampTxId()
				mock.Register(txId)
			}
		}

	}
}
func TestName(t *testing.T) {
	txResultC := make(chan *TxResultMock, 1)
	fmt.Printf("%p",txResultC)
}

func Test(t *testing.T) {
	mock := NewTxResultDispatcherMock(nil, 0)
	mock.Start()

	InitChainClient()

	block,err := CmClient.GetBlockByHeight(26529233,false)
	if err != nil {
		t.Errorf(err.Error())
	}

	mock.blockC <- block
	select {}
}

func TestBlock(t *testing.T) {


	InitChainClient()

	block,err := CmClient.GetChainInfo()
	if err != nil {
		t.Errorf(err.Error())
	}

	println(block.BlockHeight)

}
