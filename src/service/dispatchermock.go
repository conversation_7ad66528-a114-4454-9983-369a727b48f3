package service

import (
	"fmt"
	"sync"
	"time"

	"chainmaker.org/chainmaker/pb-go/v2/common"
	cmSdk "chainmaker.org/chainmaker/sdk-go/v2"
	"git.woa.com/zhixinlian/websdk/seelog"
)

var (
	resultDispatchermock *txResultDispatcherMock
)

// TxResultMock tx result
type TxResultMock struct {
	Result      *common.Result
	TxTimestamp int64
	BlockHeight uint64
}

type txResultDispatcherMock struct {
	nextBlockNum uint64
	interval     int64 //cm block interval ms
	cc           *cmSdk.ChainClient
	stopC        chan struct{}
	blockC       chan *common.BlockInfo

	mux             sync.RWMutex // mux protect pendingRegistrations
	txMux           sync.Mutex   // mux protect txRegistrations
	txRegistrations map[string]chan *TxResultMock
	pendingRegister map[string]chan *TxResultMock
}

// NewTxResultDispatcherMock tx dis
func NewTxResultDispatcherMock(cc *cmSdk.ChainClient, interval int64) *txResultDispatcherMock {
	if resultDispatchermock != nil {
		return resultDispatchermock
	}

	if interval == 0 {
		interval = int64(defaultInterval)
	}

	//chainInfo, err := cc.GetChainInfo()
	//if err != nil {
	//	panic(fmt.Errorf("init chainInfo err[%s]", err.Error()))
	//}

	seelog.Debugf("init chain last block[%d]", 0)
	resultDispatchermock = &txResultDispatcherMock{
		nextBlockNum:    0,
		interval:        interval,
		cc:              cc,
		stopC:           make(chan struct{}),
		blockC:          make(chan *common.BlockInfo, 10),
		txRegistrations: make(map[string]chan *TxResultMock, defaultTxRegisCapacity),
		pendingRegister: make(map[string]chan *TxResultMock),
	}

	return resultDispatchermock
}

// GetTxResultDispatcherMock get tx dis
func GetTxResultDispatcherMock() *txResultDispatcherMock {
	return resultDispatchermock
}

// Start dispatcher start
func (res *txResultDispatcherMock) Start() {
	go res.loop()
	go res.simSubscribe()
}

// Stop dispatcher stop
func (res *txResultDispatcherMock) Stop() {
	close(res.stopC)
}

//Register reg tx wait tx chain result
func (res *txResultDispatcherMock) Register(txId string) chan *TxResultMock {
	res.mux.Lock()
	defer res.mux.Unlock()
	if txResultC, exists := res.pendingRegister[txId]; exists {
		return txResultC
	}
	txResultC := make(chan *TxResultMock, 1)
	res.pendingRegister[txId] = txResultC
	//seelog.Infof("subscribe tx[%s] success", txId)
	return txResultC
}

//UnRegister unreg tx
func (res *txResultDispatcherMock) UnRegister(txId string) {
	res.mux.Lock()
	if _, exists := res.pendingRegister[txId]; exists {
		delete(res.pendingRegister, txId)
	}
	res.mux.Unlock()
}

//simSubscribe simulate subscribe cm block
func (res *txResultDispatcherMock) simSubscribe() {
	timeout := time.Duration(res.interval) * time.Millisecond
	ticker := time.NewTicker(timeout)
	for {
		select {
		case <-ticker.C:
			//chainInfo, err := res.cc.GetChainInfo()
			//if err != nil {
			//	ticker.Reset(timeout)
			//	break
			//}

			seelog.Debugf("new round last block[%d] success", res.nextBlockNum)
			//if chainInfo.BlockHeight < res.nextBlockNum {
			//	ticker.Reset(timeout)
			//	break
			//}

			pending := make(map[string]chan *TxResultMock)

			res.mux.Lock()
			pendingRegister := res.pendingRegister
			res.pendingRegister = pending
			res.mux.Unlock()

			seelog.Debugf("pendingRegister length is: %d,txRegistrations start length is: %d ",
				len(pendingRegister), len(res.txRegistrations))
			res.txMux.Lock()
			for txId, resultC := range pendingRegister {
				res.txRegistrations[txId] = resultC
				//seelog.Debugf("txId %s  resultC is: %p ",txId,resultC)
			}
			res.txMux.Unlock()
			seelog.Debugf("txRegistrations end length is: %d ", len(res.txRegistrations))
			//for chainInfo.BlockHeight >= res.nextBlockNum {
			//	block, err := res.cc.GetBlockByHeight(res.nextBlockNum, false)
			//	if err != nil {
			//		ticker.Reset(timeout)
			//		seelog.Errorf("query block[%d] failed,err[%s]", res.nextBlockNum, err.Error())
			//		break
			//	}
			//	seelog.Debugf("query block[%d] success", res.nextBlockNum)
			//
			//	res.nextBlockNum++
			//	res.blockC <- block
			//}
			block := &common.BlockInfo{
				Block: &common.Block{
					Txs: make([]*common.Transaction, 0),
					Header: &common.BlockHeader{
						BlockHeight: res.nextBlockNum,
					},
				},
			}
			for tx, _ := range res.txRegistrations {
				ntx := &common.Transaction{
					Payload: &common.Payload{
						TxId: tx,
					},
				}

				block.Block.Txs = append(block.Block.Txs, ntx)
			}

			//res.blockC <- block
			ticker.Reset(timeout)

		case <-res.stopC:
			return
		}
	}
}

//loop handle blockInfo and release blocked transactions.
func (res *txResultDispatcherMock) loop() {
	seelog.Infof("start handle tx result ")
	for {
		select { //循环parse blockInfo unregister
		case blockInfo := <-res.blockC:
			seelog.Debugf("receive block[%d]", blockInfo.Block.Header.BlockHeight)
			res.txMux.Lock()
			//seelog.Debugf("txRegistrations length is: %d in loop func",len(res.txRegistrations))
			looptxRegistrations := len(res.txRegistrations)
			n := 0
			for _, tx := range blockInfo.Block.Txs {
				fmt.Printf("txid [%s]\n", tx.Payload.TxId)
				if txResultC, exists := res.txRegistrations[tx.Payload.TxId]; exists {
					result := &TxResultMock{
						//Result:      tx.Result,
						//TxTimestamp: tx.Payload.Timestamp,
						//BlockHeight: blockInfo.Block.Header.BlockHeight,
					}
					// seelog.Debugf("txId[%s] send for us", tx.Payload.TxId)
					// non-blocking write to channel to ignore txResultC buffer is full in extreme cases
					select {
					case txResultC <- result:
					default:
					}
					delete(res.txRegistrations, tx.Payload.TxId)
					close(txResultC)
					n++
				}
			}
			seelog.Debugf("loop release tx number %d ,start number is %d end txRegistrations length is: %d",
				n, looptxRegistrations, len(res.txRegistrations))
			if n-looptxRegistrations != len(res.txRegistrations) {
				panic("unexpect panic")
			}
			res.txMux.Unlock()
		case <-res.stopC:
			return
		}
	}
}
