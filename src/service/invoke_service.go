/**
 * @File:  invoke_service
 * @Description: 处理复杂逻辑
 * @Author: suxiongye
 * @Date: 2022/1/24 3:54 PM
 * @Copyright: Tencent
 */

package service

import "C"
import (
	"fmt"
	"strings"
	"time"

	"chainmaker.org/chainmaker/common/v2/crypto"
	"chainmaker.org/chainmaker/pb-go/v2/accesscontrol"
	"chainmaker.org/chainmaker/sdk-go/v2/utils"

	"chainmaker.org/chainmaker/pb-go/v2/common"
	cmSdk "chainmaker.org/chainmaker/sdk-go/v2"
	"git.woa.com/zhixinlian/chain_service/src/config"
	"git.woa.com/zhixinlian/websdk/seelog"
)

var (
	defaultTimeout = 25000
)

var invokeContractMethod = "invoke_contract"

var CmClient *cmSdk.ChainClient

// ConvertArgs 参数转换
func ConvertArgs(method string, args []string, argInfoMap map[string]int) ([]*common.KeyValuePair, error) {
	kvs := []*common.KeyValuePair{
		{
			Key:   "method",
			Value: []byte(method),
		},
	}
	if len(argInfoMap) == 0 {
		return kvs, nil
	}
	// map[参数名]=》索引位置 转为 map[索引位置]=》参数名，直接遍历argInfoMap，顺序随机
	argNumMap := make(map[int]string)
	for key, index := range argInfoMap {
		argNumMap[index] = key
	}
	// 遍历fabric参数数组
	for index, value := range args {
		if argNumMap[index] != "" {
			kvs = append(kvs, &common.KeyValuePair{
				Key:   argNumMap[index],
				Value: []byte(value),
			})
		}
	}
	return kvs, nil
}

// Query 查询操作
func Query(ccName string, kvs []*common.KeyValuePair) (*cmSdk.TxResponse, error) {
	var err error
	res := &cmSdk.TxResponse{
		TxTimestamp: time.Now().Unix(),
		BlockHeight: 0,
	}
	res.Response, err = CmClient.QueryContract(ccName, invokeContractMethod, kvs, -1)
	if err != nil {
		return res, err
	}
	seelog.Infof("QUERY claim contract resp: %s\n", res.Response.String())
	return res, nil
}

// Query2 查询操作
func Query2(ccName, contractMethod string, kvs []*common.KeyValuePair) (*cmSdk.TxResponse, error) {
	var err error
	res := &cmSdk.TxResponse{
		TxTimestamp: time.Now().Unix(),
		BlockHeight: 0,
	}
	res.Response, err = CmClient.QueryContract(ccName, contractMethod, kvs, -1)
	if err != nil {
		return res, err
	}
	seelog.Infof("QUERY claim contract resp: %s\n", res.Response.String())
	return res, nil
}

// Invoke 修改操作
func Invoke(contractName, txId string, kvs []*common.KeyValuePair, withSyncResult bool,
	gasLimit uint64) (*cmSdk.TxResponse, error) {
	invokeStartTime := time.Now()
	seelog.Infof("invoke started: contract[%s], tx[%s], withSyncResult=%v, gasLimit=%d",
		contractName, txId, withSyncResult, gasLimit)
	// 调用合约
	sdkStartTime := time.Now()
	resp, err := CmClient.InvokeContractWithLimitV2(contractName, invokeContractMethod, txId, kvs, -1, withSyncResult,
		&common.Limit{GasLimit: gasLimit})
	sdkDuration := time.Since(sdkStartTime)
	if err != nil {
		seelog.Errorf("InvokeContractWithLimitV2 failed for tx[%s]: %v, duration=%v", txId, err, sdkDuration)
		return nil, err
	}
	seelog.Infof("InvokeContractWithLimitV2 completed for tx[%s]: code[%d], duration=%v",
		txId, resp.Response.Code, sdkDuration)
	// 如果需要同步等待结果
	if !config.Conf.Common.EnableSubscribe {
		seelog.Infof("EnableSubscribe=false, starting asyncTxResult for tx[%s]", txId)
		asyncStartTime := time.Now()
		result, err := asyncTxResult(txId)
		asyncDuration := time.Since(asyncStartTime)
		if err != nil {
			seelog.Errorf("asyncTxResult failed for tx[%s]: %v, duration=%v", txId, err, asyncDuration)
			return nil, err
		}
		seelog.Infof("asyncTxResult completed for tx[%s]: block[%d], code[%d], duration=%v",
			txId, result.BlockHeight, result.Result.Code, asyncDuration)
		// 更新响应结果
		resp.Response.Code = result.Result.Code
		resp.Response.Message = result.Result.Message
		resp.Response.ContractResult = result.Result.ContractResult
		resp.Response.TxId = txId
		resp.TxTimestamp = result.TxTimestamp
		resp.BlockHeight = result.BlockHeight
	} else {
		seelog.Infof("EnableSubscribe=true, skipping asyncTxResult for tx[%s]", txId)
	}
	// 记录总体执行时间
	totalDuration := time.Since(invokeStartTime)
	// 慢请求特殊标识日志
	if totalDuration > 500*time.Millisecond {
		seelog.Warnf("SLOW_REQUEST: tx[%s] cost too much time, total_duration=%v, sdk_duration=%v",
			txId, totalDuration, sdkDuration)
	}

	if resp.Response.Code != common.TxStatusCode_SUCCESS {
		seelog.Errorf("invoke failed for tx[%s]: code[%d], message[%s], total_duration=%v",
			txId, resp.Response.Code, resp.Response.Message, totalDuration)

		result := resp.Response.ContractResult.Result
		resp.Response.ContractResult.Result = nil
		errStr := strings.Replace(resp.Response.ContractResult.String()+" result:"+string(result), "code", "contract_code", 1)

		return resp, fmt.Errorf(errStr)
	} else if resp.Response.ContractResult.Code != 0 {
		seelog.Infof("invoke contract err, resp: %s\n", resp.Response.String())

		result := resp.Response.ContractResult.Result
		resp.Response.ContractResult.Result = nil
		errStr := strings.Replace(resp.Response.ContractResult.String()+" result:"+string(result), "code", "contract_code", 1)

		return resp, fmt.Errorf(errStr)
	}

	seelog.Infof("invoke completed successfully for tx[%s]: code[%d], total_duration=%v",
		txId, resp.Response.Code, totalDuration)

	if !withSyncResult {
		seelog.Debugf("invoke contract success (no sync), resp: %s", resp.Response.String())
	} else {
		seelog.Debugf("invoke contract withSyncResult success, resp: %s", resp.Response.String())
	}
	return resp, nil
}

// InitChainClient init chain
func InitChainClient() error {
	var err error

	// 创建seelog适配器，让SDK使用统一的日志系统
	seelogAdapter := NewSeelogAdapter()

	if config.Conf.Common.EnableSubscribe {
		CmClient, err = cmSdk.NewChainClient(
			cmSdk.WithConfPath(config.Conf.Chain.SdkConfigPath),
			cmSdk.WithEnableTxResultDispatcher(true),
			cmSdk.WithChainClientLogger(seelogAdapter),
		)
	} else {
		CmClient, err = cmSdk.NewChainClient(
			cmSdk.WithConfPath(config.Conf.Chain.SdkConfigPath),
			cmSdk.WithChainClientLogger(seelogAdapter),
		)
	}
	if err != nil {
		return err
	}
	// Enable certificate compression
	if CmClient.GetAuthType() == cmSdk.PermissionedWithCert {
		err = CmClient.EnableCertHash()
	}
	if err != nil {
		return err
	}

	return nil
}

func asyncTxResult(txId string) (*TxResult, error) {
	startTime := time.Now()
	seelog.Infof("asyncTxResult started for tx[%s], timeout=%dms", txId, defaultTimeout)

	// 注册等待交易结果
	registerStartTime := time.Now()
	txResultC := GetTxResultDispatcher().Register(txId)
	registerDuration := time.Since(registerStartTime)
	seelog.Debugf("tx[%s] registered to dispatcher, duration=%v", txId, registerDuration)

	timeout := time.Duration(defaultTimeout) * time.Millisecond
	ticker := time.NewTicker(timeout)
	defer ticker.Stop()

	select {
	case r := <-txResultC:
		totalDuration := time.Since(startTime)
		seelog.Infof("tx[%s] result received successfully, block[%d], code[%d], total_duration=%v",
			txId, r.BlockHeight, r.Result.Code, totalDuration)
		return r, nil

	case <-ticker.C:
		totalDuration := time.Since(startTime)
		seelog.Errorf("tx[%s] result timed out after %v, unregistering...", txId, totalDuration)

		unregisterStartTime := time.Now()
		GetTxResultDispatcher().UnRegister(txId)
		unregisterDuration := time.Since(unregisterStartTime)
		seelog.Debugf("tx[%s] unregistered from dispatcher, duration=%v", txId, unregisterDuration)

		return nil, fmt.Errorf("get transaction[%s] result timed out, timeout=%s, total_duration=%v",
			txId, timeout, totalDuration)
	}
}

// InvokeLocalSign 修改操作
func InvokeLocalSign(contractName, contractMethod, txId string, kvs []*common.KeyValuePair, withSyncResult bool,
	gasLimit uint64) (*cmSdk.TxResponse, error) {
	resp := &cmSdk.TxResponse{
		Response: &common.TxResponse{
			ContractResult: &common.ContractResult{},
		},
	}
	var err error
	payload := CmClient.CreatePayload(txId, common.TxType_INVOKE_CONTRACT, contractName, contractMethod, kvs, 0,
		&common.Limit{GasLimit: gasLimit})

	req, err := GenerateTxRequest(payload, nil)
	if err != nil {
		seelog.Infof("GenerateTxRequest err, err: %s\n", err.Error())
		return nil, err
	}

	txresp, err := CmClient.SendTxRequest(req, -1, withSyncResult)
	if err != nil {
		seelog.Infof("invoke contract err, err: %s\n", err.Error())
		return nil, err
	}

	if !config.Conf.Common.EnableSubscribe && txresp.Code == common.TxStatusCode_SUCCESS {
		seelog.Infof("start subscribe tx[%s] \n", txId)
		result, err := asyncTxResult(txId)
		if err != nil {
			seelog.Infof("subscribe tx[%s] err, err: %s\n", txId, err.Error())
			return nil, err
		}
		resp.Response.Code = result.Result.Code
		resp.Response.Message = result.Result.Message
		resp.Response.ContractResult = result.Result.ContractResult
		resp.Response.TxId = txId
		resp.TxTimestamp = result.TxTimestamp
		resp.BlockHeight = result.BlockHeight
	} else {
		resp.Response.Code = txresp.Code
		resp.Response.Message = txresp.Message
		resp.Response.ContractResult = txresp.ContractResult
		resp.Response.TxId = txId
		//resp.TxTimestamp = txresp.TxTimestamp
		//resp.BlockHeight = txresp.BlockHeight
	}

	if txresp.Code != common.TxStatusCode_SUCCESS {
		seelog.Infof("invoke contract err, resp: %s\n", txresp.String())
		var errStr string
		if txresp.ContractResult != nil {
			result := txresp.ContractResult.Result
			resp.Response.ContractResult.Result = nil
			errStr = strings.Replace(resp.Response.ContractResult.String()+" result:"+string(result), "code", "contract_code", 1)
		} else {
			errStr = txresp.Message
		}

		return resp, fmt.Errorf(errStr)
	} else if resp.Response.ContractResult.Code != 0 {
		seelog.Infof("invoke contract err, resp: %s\n", txresp.String())

		var errStr string
		result := resp.Response.ContractResult.Result
		resp.Response.ContractResult.Result = nil
		errStr = strings.Replace(resp.Response.ContractResult.String()+" result:"+string(result), "code", "contract_code", 1)
		return resp, fmt.Errorf(errStr)
	}
	if !withSyncResult {
		seelog.Infof("invoke contract success, resp: %s\n", resp.Response.String())
	} else {
		seelog.Infof("invoke contract withSyncResult success, resp: %s\n", resp.Response.String())
	}
	return resp, nil
}

// GenerateTxRequest 生成请求结构体
func GenerateTxRequest(payload *common.Payload,
	endorsers []*common.EndorsementEntry) (*common.TxRequest, error) {
	var (
		signer    *accesscontrol.Member
		signBytes []byte
		err       error
	)

	// 构造Sender
	signer = &accesscontrol.Member{
		OrgId:      config.Conf.GasAdminConfig.OrgId,
		MemberInfo: config.Conf.GasAdminConfig.PkByte,
		MemberType: accesscontrol.MemberType_PUBLIC_KEY,
	}

	req := &common.TxRequest{
		Payload: payload,
		Sender: &common.EndorsementEntry{
			Signer:    signer,
			Signature: nil,
		},
		Endorsers: endorsers,
	}

	signBytes, err = utils.SignPayloadWithHashType(config.Conf.GasAdminConfig.PrivateKey,
		crypto.HashAlgoMap[config.Conf.GasAdminConfig.HashType], payload)
	if err != nil {
		return nil, fmt.Errorf("SignPayload failed, %s", err.Error())
	}

	req.Sender.Signature = signBytes

	return req, nil
}
