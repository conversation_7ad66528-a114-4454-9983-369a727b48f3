package main

import (
	"fmt"

	"github.com/gin-gonic/gin"

	"git.woa.com/zhixinlian/chain_service/src/config"
	"git.woa.com/zhixinlian/chain_service/src/ctrl"
	"git.woa.com/zhixinlian/chain_service/src/initialize"
	"git.woa.com/zhixinlian/websdk"
	"git.woa.com/zhixinlian/websdk/seelog"
)

func main() {
	err := initialize.InitResource()
	fmt.Println("InitResource ok version 2.3.1")
	if err != nil {
		fmt.Printf("initialize.InitResource err %s", err.Error())
		_ = seelog.Errorf("initialize.InitResource err %s", err.Error())
		return
	}
	router := websdk.CreateTBaasGin()
	fmt.Println("CreateTBaasGin ok")
	registerRouter(router)
	fmt.Printf("Start ok, listen at port:%v\n", config.Conf.Common.Port)
	err = websdk.Run(router, fmt.Sprintf(":%d", config.Conf.Common.Port))
	if err != nil {
		fmt.Printf("router.Run err %s", err.Error())
		_ = seelog.Errorf("router.Run err %s", err.Error())
		return
	}
}

func registerRouter(router *gin.Engine) {
	v2 := router.Group("/api/v2")
	{
		v2.POST("/chain/invoke", ctrl.InvokeV3)
		v2.POST("/cm_invoke", ctrl.CmInvoke)
		v2.POST("/gas_recharge", ctrl.CmInvokeGas)
		v2.POST("/gas_balances", ctrl.CmInvokeGasQuery)
	}
}