// Package initialize 初始化
package initialize

import (
	"git.woa.com/zhixinlian/chain_service/src/config"
	"git.woa.com/zhixinlian/chain_service/src/pulsar"
	"git.woa.com/zhixinlian/chain_service/src/service"
)

// InitResource 初始化资源配置
func InitResource() error {
	err := InitInfra()
	if err != nil {
		return err
	}
	// 初始化长安链服务
	err = service.InitChainClient()
	if err != nil {
		return err
	}
	//初始化模拟订阅模块
	if !config.Conf.Common.EnableSubscribe {
		service.NewTxResultDispatcher(service.CmClient, 0).Start()
	}
	//init pulsarService
	if config.Conf.Pulsar.Enable {
		err = pulsar.InitPulsarService()
		if err != nil {
			return err
		}
	}

	return nil
}

// InitInfra 预留接口
func InitInfra() error {
	return nil
}
