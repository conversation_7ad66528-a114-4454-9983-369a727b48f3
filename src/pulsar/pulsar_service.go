package pulsar

import (
	"context"
	"encoding/json"
	"git.woa.com/zhixinlian/websdk/seelog"
	"time"

	"git.woa.com/zhixinlian/chain_service/src/config"
	"github.com/apache/pulsar-client-go/pulsar"
)

var pulsarProducer pulsar.Producer

//InitPulsarService 初始化 pulsar 服务
func InitPulsarService() error {
	if !config.Conf.Pulsar.Enable {
		pulsarProducer = nil
		return nil
	}

	client, err := pulsar.NewClient(pulsar.ClientOptions{
		URL:               config.Conf.Pulsar.URL,
		Authentication:    pulsar.NewAuthenticationToken(config.Conf.Pulsar.Token),
		OperationTimeout:  time.Duration(config.Conf.Pulsar.OperationTimeout) *time.Second,
		ConnectionTimeout: time.Duration(config.Conf.Pulsar.ConnectionTimeout) * time.Second,
	})

	if err != nil {
		return err
	}

	// 使用客户端创建生产者
	pulsarProducer, err = client.CreateProducer(pulsar.ProducerOptions{
		// topic完整路径，格式为persistent://集群（租户）ID/命名空间/Topic名称
		Topic: config.Conf.Pulsar.Topic,
	})
	if err != nil {
		return err
	}

	return nil
}

//Send send
func Send(message interface{}) {
	if pulsarProducer == nil {
		return
	}
	seelog.Infof("send req [%+v] err[%+v]",message)
	// 将业务请求写入消息队列
	reqByte , err := json.Marshal(message)
	if err != nil {
		_ = seelog.Errorf("json marshal req [%+v] err[%+v]",message,err)
	}else {
		_,err = GetPulsarProducer().Send(context.Background(),&pulsar.ProducerMessage{
			// 消息内容
			Payload: reqByte,
			// 业务key
			Key: "" } )
		if err != nil {
			_ = seelog.Errorf("pulsar producer send fail,err[%+v]",err)
		}
	}
	seelog.Infof("send req successful [%+v] err[%+v]",message)
}

//GetPulsarProducer Get PulsarProducer
func GetPulsarProducer() pulsar.Producer{
	return pulsarProducer
}