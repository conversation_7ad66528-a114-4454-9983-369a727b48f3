package ctrl

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"chainmaker.org/chainmaker/pb-go/v2/common"
	cmSdk "chainmaker.org/chainmaker/sdk-go/v2"
	"chainmaker.org/chainmaker/sdk-go/v2/utils"
	"git.woa.com/zhixinlian/chain_service/src/config"
	"git.woa.com/zhixinlian/chain_service/src/constant"
	"git.woa.com/zhixinlian/chain_service/src/ctrl/model"
	"git.woa.com/zhixinlian/chain_service/src/service"
	"git.woa.com/zhixinlian/websdk/handler"
	"git.woa.com/zhixinlian/websdk/response"
	"git.woa.com/zhixinlian/websdk/seelog"
)

// InvokeHandler 链服务参数
type InvokeHandler struct {
	Req  model.CmInvokeReq
	Resp model.InvokeResp
}

// CmInvoke chainmaker原生接口
func CmInvoke(c *gin.Context) {
	var (
		hd  InvokeHandler
		res *response.RetData
	)

	if err := c.ShouldBind(&hd.Req); err != nil {
		_ = seelog.Errorf("shouldBind err %s", err.Error())
		res = response.NewRetData(err, nil)
		c.JSON(http.StatusOK, *res)
		return
	}

	err := handler.Run(&hd)
	if err != nil {
		_ = seelog.Errorf("run error:%s", err)
		res = response.NewRetData(err, nil)
	} else {
		res = response.BuildSuccResp(hd.Resp)
	}
	c.JSON(http.StatusOK, *res)
}

// HandleInput check 链参数
func (p *InvokeHandler) HandleInput() error {
	// 错误码后续根据链服务标准再补充定义，目前暂通用 RESP_PARAMS_INVALID
	_, ok := constant.CmMethodByType[p.Req.Fcn]
	if p.Req.ChaincodeName == "" {
		_ = seelog.Errorf("input chaincodeName invalid")
		return constant.ErrCodeChainCodeNameNotExist
	}

	if !ok {
		_ = seelog.Errorf("input function invalid.")
		return constant.ErrCodeFuncNotExist
	}

	if len(config.Conf.FuncInfoMap[p.Req.ChaincodeName]) == 0 {
		_ = seelog.Errorf("input chaincodeName invalid")
		return constant.ErrCodeChainCodeNameUndefined
	}

	if _, ok := config.Conf.FuncInfoMap[p.Req.ChaincodeName][string(p.Req.Args["method"])]; !ok {
		_ = seelog.Errorf("input key [method] invalid")
		return constant.ErrCodeChainCodeInputError
	}

	return nil
}

// HandleProcess 原生链调用处理
func (p *InvokeHandler) HandleProcess() error {
	funcName := string(p.Req.Args["method"])
	if funcName == "" {
		_ = seelog.Errorf("input invalid contract func name")
		return constant.ErrCodeChainCodeFuncNotExist
	}

	var gasLimit = config.Conf.FuncInfoMap[p.Req.ChaincodeName][funcName].GasLimit
	var err error
	var txResp *cmSdk.TxResponse

	kvs := make([]*common.KeyValuePair, 0, len(p.Req.Args))
	for key, val := range p.Req.Args {
		kv := &common.KeyValuePair{
			Key:   key,
			Value: val,
		}
		kvs = append(kvs, kv)
	}

	// query - 查询方法，不生成读写集，不消耗gas； invoke - 产生读写集且消耗gas
	if constant.CmMethodByType[p.Req.Fcn] == constant.CmMethodTypeQuery {
		txResp, err = service.Query(p.Req.ChaincodeName, kvs)
		if err != nil {
			_ = seelog.Errorf("failed to invoke %+v\n", err)
			return response.Build(2002,err.Error())
		}
	} else if constant.CmMethodByType[p.Req.Fcn] == constant.CmMethodTypeInvoke {
		txId := utils.GetTimestampTxId()
		withSync := config.Conf.Common.EnableSubscribe
		txResp, err = service.Invoke(p.Req.ChaincodeName, txId, kvs, withSync, gasLimit)
		if err != nil {
			_ = seelog.Errorf("invoke txId[] failed to invoke %+v\n",txId, err)
			return response.Build(2002,err.Error())
		}
	}

	p.Resp.Data = &model.ChainInvokeRespData{}
	p.Resp.Data.BlockHeight = txResp.BlockHeight
	p.Resp.Data.Payload = string(txResp.Response.ContractResult.GetResult())
	p.Resp.Data.TxHash = txResp.Response.TxId
	p.Resp.Data.CreateTime = time.Unix(txResp.TxTimestamp, 0).Format("2006-01-02 15:04:05")

    seelog.Infof("[%+v]",p.Resp)
	return nil
}
