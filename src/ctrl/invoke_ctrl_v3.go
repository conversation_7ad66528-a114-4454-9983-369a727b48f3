package ctrl

import (
	"git.woa.com/zhixinlian/chain_service/src/pulsar"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	cmSdk "chainmaker.org/chainmaker/sdk-go/v2"
	"chainmaker.org/chainmaker/sdk-go/v2/utils"
	"git.woa.com/zhixinlian/chain_service/src/config"
	"git.woa.com/zhixinlian/chain_service/src/constant"
	"git.woa.com/zhixinlian/chain_service/src/ctrl/model"
	"git.woa.com/zhixinlian/chain_service/src/service"
	"git.woa.com/zhixinlian/websdk/handler"
	"git.woa.com/zhixinlian/websdk/response"
	"git.woa.com/zhixinlian/websdk/seelog"
)

// ChainInvokeHandlerV3 链服务
type ChainInvokeHandlerV3 struct {
	Req  model.InvokeReq
	Resp model.InvokeResp
}

// InvokeV3 链服务执行方法
func InvokeV3(c *gin.Context) {
	var (
		hd  ChainInvokeHandlerV3
		res *response.RetData
	)

	if err := c.ShouldBind(&hd.Req); err != nil {
		res = response.NewRetData(err, nil)
		res.RetMsg = err.Error()
		res.RetCode = 0 // int(response.RESP_PARAMS_INVALID)
		c.JSON(http.StatusOK, *res)
		return
	}

	if err := handler.Run(&hd); err != nil {
		res = response.NewRetData(err, nil)
		c.JSON(http.StatusOK, *res)
		return
	}
	c.JSON(http.StatusOK, hd.Resp)
}

// HandleInput check链请求参数
func (p *ChainInvokeHandlerV3) HandleInput() error {
	// 错误码后续根据链服务标准再补充定义，目前暂通用 RESP_PARAMS_INVALID
	if p.Req.ChaincodeName == "" {
		_ = seelog.Errorf("input chaincodeName invalid")
		return constant.ErrCodeChainCodeNameNotExist
	}

	if p.Req.Fcn == "" {
		_ = seelog.Errorf("input chaincode function invalid.")
		return constant.ErrCodeFuncNotExist
	}

	if len(config.Conf.FuncInfoMap[p.Req.ChaincodeName]) == 0 {
		_ = seelog.Errorf("input chaincodeName invalid")
		return constant.ErrCodeChainCodeNameUndefined
	}
	// 将业务交易写入消息队列
	pulsar.Send(p.Req)
	return nil
}

// HandleProcess 处理上链请求
func (p *ChainInvokeHandlerV3) HandleProcess() error {
	var funcInfo = config.Conf.FuncInfoMap[p.Req.ChaincodeName][p.Req.Fcn]
	var txResp *cmSdk.TxResponse

	if funcInfo.GasLimit == 0 {
		_ = seelog.Errorf("input fcn[%s] invalid ", p.Req.Fcn)
		return constant.ErrCodeChainCodeFuncNotExist
	}

	kvs, err := service.ConvertArgs(p.Req.Fcn, p.Req.Args, funcInfo.ArgInfoMap)
	if err != nil {
		_ = seelog.Errorf("input invalid.Err: %s", err.Error())
		return constant.RespParamsInvalid
	}

	// query - 查询方法，不生成读写集，不消耗gas； invoke - 产生读写集且消耗gas
	if funcInfo.MethodType == constant.CmMethodTypeQuery {
		txResp, err = service.Query(p.Req.ChaincodeName, kvs)
		if err != nil {
			_ = seelog.Errorf("failed to invoke %+v\n", err)
			return response.Build(2002,err.Error())
		}
	} else if funcInfo.MethodType == constant.CmMethodTypeInvoke {
		txId := utils.GetTimestampTxId()
		withSync := config.Conf.Common.EnableSubscribe

		txResp, err = service.Invoke(p.Req.ChaincodeName, txId, kvs, withSync, funcInfo.GasLimit)
		if err != nil {
			_ = seelog.Errorf("invoke txId[%s] failed to invoke err: %+v\n",txId, err)
			return response.Build(2002,err.Error())
		}
	}else {
		_ = seelog.Errorf("not except method type,type[%d]\n",funcInfo.MethodType)
		return constant.ErrCodeChainCodeFuncNotExist
	}

	p.Resp.Data = &model.ChainInvokeRespData{}
	p.Resp.Data.BlockHeight = txResp.BlockHeight
	p.Resp.Data.Payload = string(txResp.Response.ContractResult.Result)
	p.Resp.Data.TxHash = txResp.Response.TxId
	p.Resp.Data.CreateTime = time.Unix(txResp.TxTimestamp, 0).Format("2006-01-02 15:04:05")

	seelog.Infof("handle transaction resp [%+v]\n",p.Resp)
	return nil
}
