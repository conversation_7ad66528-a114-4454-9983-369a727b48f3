package model

// InvokeReq 链服务请求
type InvokeReq struct {
	ChannelName   string   `json:"channelName"`
	ChaincodeName string   `json:"chaincodeName"`
	Fcn           string   `json:"fcn"`
	IsNeedTime    bool     `json:"isNeedTime"`
	IsNeedTsa     bool     `json:"isNeedTsa"`
	Args          []string `json:"args"`
}

// CmInvokeReq chainmaker 原生请求
type CmInvokeReq struct {
	ChannelName   string            `json:"channelName"`
	ChaincodeName string            `json:"chaincodeName"`
	Fcn           string            `json:"fcn"` // query or invoke
	IsNeedTime    bool              `json:"isNeedTime"`
	IsNeedTsa     bool              `json:"isNeedTsa"`
	Args          map[string][]byte `json:"args"`
}

// CmInvokeReqGas chainmaker 原生请求
type CmInvokeReqGas struct {
	Address string `json:"address"`
	Amount  uint64 `json:"amount"`
}

// CmInvokeReqGasQuery chainmaker 原生请求
type CmInvokeReqGasQuery struct {
	Addresses []string `json:"addresses"`
}

// InvokeResp 链服务响应
type InvokeResp struct {
	RetCode int                  `json:"retCode"`
	RetMsg  string               `json:"retMsg"`
	Data    *ChainInvokeRespData `json:"data"`
}

// QueryGasResp Gas 查询响应
type QueryGasResp struct {
	RetCode int                  `json:"retCode"`
	RetMsg  string               `json:"retMsg"`
	Data    []*AddressGasBalance `json:"data"`
}

// ChainInvokeRespData 链服务返回数据
type ChainInvokeRespData struct {
	Ext         string `json:"ext"`
	BlockHeight uint64 `json:"blockHeight"`
	TxHash      string `json:"txHash"`
	CreateTime  string `json:"createTime"`
	Payload     string `json:"payload"`
}

// AddressGasBalance 地址 gas 余额
type AddressGasBalance struct {
	Address string `json:"address"`
	Balance uint64 `json:"balance"`
}
