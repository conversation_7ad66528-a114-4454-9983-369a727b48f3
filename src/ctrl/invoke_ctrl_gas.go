package ctrl

import (
	"chainmaker.org/chainmaker/pb-go/v2/syscontract"
	"fmt"
	"git.woa.com/zhixinlian/chain_service/src/constant"
	"net/http"
	"strconv"
	"time"

	"chainmaker.org/chainmaker/pb-go/v2/common"
	cmSdk "chainmaker.org/chainmaker/sdk-go/v2"
	"chainmaker.org/chainmaker/sdk-go/v2/utils"
	"git.woa.com/zhixinlian/chain_service/src/config"
	"git.woa.com/zhixinlian/chain_service/src/ctrl/model"
	"git.woa.com/zhixinlian/chain_service/src/service"
	"git.woa.com/zhixinlian/websdk/response"
	"git.woa.com/zhixinlian/websdk/seelog"
	"github.com/gin-gonic/gin"
)

// GasInvokeHandler 链服务参数
type GasInvokeHandler struct {
	Req  model.CmInvokeReqGas
	Resp model.InvokeResp
}

// GasQueryInvokeHandler 链服务参数
type GasQueryInvokeHandler struct {
	Req  model.CmInvokeReqGasQuery
	Resp model.QueryGasResp
}

// CmInvokeGas chainmaker原生接口
func CmInvokeGas(c *gin.Context) {
	var (
		hd  GasInvokeHandler
		res *response.RetData
	)

	if err := c.ShouldBind(&hd.Req); err != nil {
		_ = seelog.Errorf("shouldBind err %s", err.Error())
		res = response.NewRetData(err, nil)
		c.JSON(http.StatusOK, *res)
		return
	}

	if hd.Req.Amount == 0 && hd.Req.Address == "" {
		_ = seelog.Errorf("amount or address is invalid ")
		res = response.NewRetData(constant.RespParamsInvalid, nil)
		c.JSON(http.StatusOK, *res)
		return
	}

	err := hd.HandleProcess()
	if err != nil {
		_ = seelog.Errorf("run error:%s", err)
		res = response.NewRetData(err, nil)
		c.JSON(http.StatusOK, *res)
		return
	}
	c.JSON(http.StatusOK, hd.Resp)
}

// CmInvokeGasQuery chainmaker原生接口
func CmInvokeGasQuery(c *gin.Context) {
	var (
		hd  GasQueryInvokeHandler
		res *response.RetData
	)

	if err := c.ShouldBind(&hd.Req); err != nil {
		_ = seelog.Errorf("shouldBind err %s", err.Error())
		res = response.NewRetData(err, nil)
		c.JSON(http.StatusOK, *res)
		return
	}
	if len(hd.Req.Addresses) == 0 && len(hd.Req.Addresses) >= 100 {
		_ = seelog.Errorf("address is invalid ")
		res = response.NewRetData(constant.RespParamsInvalid, nil)
		c.JSON(http.StatusOK, *res)
		return
	}

	err := hd.HandleProcess()
	if err != nil {
		_ = seelog.Errorf("run error:%s", err)
		res = response.NewRetData(err, nil)
		c.JSON(http.StatusOK, *res)
		return
	}
	c.JSON(http.StatusOK, hd.Resp)
}

// HandleProcess 原生链调用处理gas query balance
func (p *GasQueryInvokeHandler) HandleProcess() error {
	p.Resp.Data = make([]*model.AddressGasBalance, 0)
	for _, address := range p.Req.Addresses {
		kvs := []*common.KeyValuePair{
			{
				Key:   utils.KeyGasAddressKey,
				Value: []byte(address),
			},
		}
		chaincodeName := syscontract.SystemContract_ACCOUNT_MANAGER.String()
		contractMethod := syscontract.GasAccountFunction_GET_BALANCE.String()

		txResp, err := service.Query2(chaincodeName, contractMethod, kvs)
		if err != nil {
			_ = seelog.Errorf("invoke txId[%s] failed to invoke %+v\n", txResp.Response.TxId, err)
			return response.Build(2002, err.Error())
		}

		if err = utils.CheckProposalRequestResp(txResp.Response, true); err != nil {
			return response.Build(2002, fmt.Errorf("%s failed, %s", txResp.Response.TxId, err).Error())
		}

		balance, err := strconv.ParseInt(string(txResp.Response.ContractResult.Result), 10, 64)
		if err != nil {
			return fmt.Errorf("%s failed, %s", "strconv.ParseInt", err)
		}
		p.Resp.Data = append(p.Resp.Data, &model.AddressGasBalance{
			Address: address,
			Balance: uint64(balance),
		})

	}

	return nil
}

// HandleProcess 原生链调用处理gas recharge
func (p *GasInvokeHandler) HandleProcess() error {
	//funcName := "gasTransfer"
	var gasLimit = uint64(********)
	var err error
	var txResp *cmSdk.TxResponse

	rechargeGasList := []*syscontract.RechargeGas{
		{
			Address:   p.Req.Address,
			GasAmount: int64(p.Req.Amount),
		},
	}
	rechargeGasReq := syscontract.RechargeGasReq{BatchRechargeGas: rechargeGasList}
	bz, err := rechargeGasReq.Marshal()
	if err != nil {
		return err
	}

	pairs := []*common.KeyValuePair{
		{
			Key:   utils.KeyGasBatchRecharge,
			Value: bz,
		},
	}

	txId := utils.GetTimestampTxId()
	withSync := config.Conf.Common.EnableSubscribe
	chaincodeName := syscontract.SystemContract_ACCOUNT_MANAGER.String()
	contractMethod := syscontract.GasAccountFunction_RECHARGE_GAS.String()

	txResp, err = service.InvokeLocalSign(chaincodeName, contractMethod, txId, pairs, withSync, gasLimit)
	if err != nil {
		_ = seelog.Errorf("invoke txId[%s] failed to invoke %+v\n", txId, err)
		return response.Build(2002, err.Error())
	}

	p.Resp.Data = &model.ChainInvokeRespData{}
	p.Resp.Data.BlockHeight = txResp.BlockHeight
	p.Resp.Data.Payload = string(txResp.Response.ContractResult.GetResult())
	p.Resp.Data.TxHash = txResp.Response.TxId
	p.Resp.Data.CreateTime = time.Unix(txResp.TxTimestamp, 0).Format("2006-01-02 15:04:05")
	return nil
}
