package constant

import "git.woa.com/zhixinlian/websdk/response"

// 定义服务
var (
	RespParamsInvalid *response.RetBase

	ErrCodeChannelNotExist       *response.RetBase
	ErrCodeChainCodeErr          *response.RetBase
	ErrCodeEventRegisterErr      *response.RetBase
	ErrCodeChainCodeNameNotExist *response.RetBase
	ErrCodeFuncNotExist          *response.RetBase
	ErrCodeChainCodeFuncNotExist *response.RetBase
	ErrCodeChainCodeInputError   *response.RetBase

	ErrCodeChainCodeNameUndefined *response.RetBase
)

func init() {
	RespParamsInvalid = response.Build(1001, "请求参数无效")

	ErrCodeChannelNotExist = response.Build(2001, "链Id不存在")
	ErrCodeChainCodeErr = response.Build(2002, "智能合约执行错误")
	ErrCodeEventRegisterErr = response.Build(2003, "事件注册错误")
	ErrCodeChainCodeNameNotExist = response.Build(2004, "智能合约不存在")
	ErrCodeFuncNotExist = response.Build(2005, "执行链操作不存在，必须是query,invoke")
	ErrCodeChainCodeInputError = response.Build(2006, "智能合约参数错误")
	ErrCodeChainCodeFuncNotExist = response.Build(2005, "智能合约方法不存在")

	ErrCodeChainCodeNameUndefined = response.Build(3001, "智能合约未定义")
}
