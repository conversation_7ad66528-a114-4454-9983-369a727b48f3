// Package config 配置
package config

import (
	"chainmaker.org/chainmaker/common/v2/crypto"
	"chainmaker.org/chainmaker/common/v2/crypto/asym"
	"fmt"
	"io/ioutil"
	"os"

	"git.woa.com/zhixinlian/websdk/configcenter"
	"git.woa.com/zhixinlian/websdk/seelog"
	"github.com/BurntSushi/toml"
)

// Conf 全局config变量
var Conf *TomlConfig

var defaultENV = "test"

// TomlConfig 链服务配置结构
type TomlConfig struct {
	Common         commonConfig
	MySQL          mysqlConfig
	Redis          redisConfig
	CA             caConfig
	DcronJob       DcronJobConfig
	ServiceDomain  ServiceDomainConfig
	Pulsar         pulsarConfig
	Chain          chainConfig
	FuncInfoMap    map[string]map[string]FuncInfo
	GasAdminConfig gasAdminConfig
}

type chainConfig struct {
	SdkConfigPath string `toml:"sdk_config_path"`
}

type gasAdminConfig struct {
	PrivateKeyPath string `toml:"private_key_path"`
	OrgId          string `toml:"org_id"`
	HashType       string `toml:"hash_type"`
	PrivateKey     crypto.PrivateKey
	PkByte         []byte
}

type pulsarConfig struct {
	Enable            bool   `toml:"enable"`
	URL               string `toml:"url"`
	Token             string `toml:"token"`
	Topic             string `toml:"topic"`
	Consumer          string `toml:"consumer"`
	OperationTimeout  int    `toml:"operation_timeout"`
	ConnectionTimeout int    `toml:"connection_timeout"`
	RetryTimes        int    `toml:"retry_times"`
}

type commonConfig struct {
	Port            int  `toml:"port"`
	OpenTLS         bool `toml:"open_tls"`
	EnableSubscribe bool `toml:"enable_subscribe"`
}

type mysqlConfig struct {
	URL    string `toml:"url"`
	User   string `toml:"user"`
	Pwd    string `toml:"pwd"`
	Dbname string `toml:"db_name"`
}

type redisConfig struct {
	URL string `toml:"url"`
	Pwd string `toml:"pwd"`
}

type caConfig struct {
	TrustsPath string `toml:"trusts_path"`
	ServerCrt  string `toml:"server_crt"`
	ServerKey  string `toml:"server_key"`
}

// DcronJobConfig TODO
type DcronJobConfig struct {
	DemoJobDesc string `toml:"demo_job_desc"`
	DemoJobCron string `toml:"demo_job_cron"`
}

// ServiceDomainConfig 服务域名配置
type ServiceDomainConfig struct {
	Ump   string `toml:"ump"`
	Tmw   string `toml:"tmw"`
	Dlock string `toml:"dlock"`
}

// FuncInfo 方法详情
type FuncInfo struct {
	FuncName   string         `toml:"func_name"`
	MethodType int            `toml:"method_type"`
	GasLimit   uint64         `toml:"gas_limit"`
	ArgInfoMap map[string]int `toml:"arg_info_map"`
}

// LoadConfig lod config
func (c *TomlConfig) LoadConfig(env string) {
	if env == "" {
		env = defaultENV
	}
	filePath := "/data/zx/chain_service/config/config-" + env + ".toml"
	if env == "local" {
		filePath = "config/config-local.toml"
	}
	fmt.Println("filepath ", filePath)
	if _, err := os.Stat(filePath); err != nil {
		fmt.Println(err)
		panic(err)
	}
	if _, err := toml.DecodeFile(filePath, &c); err != nil {
		fmt.Println(err)
		panic(err)
	}
}

// 打印信息
const (
	USAGE = "Usage: taskman [-e <dev|test|prod|test2>]"
)

// GetConfEnv get 环境变量
func GetConfEnv() string {
	usage := "./main {$env} "
	env := os.Getenv("ZX_ENV")
	if env == "" {
		if len(os.Args) < 2 {
			fmt.Println("not enough params, usage: ", usage)
			os.Exit(1)
		}
		if len(os.Args) >= 4 {
			env = defaultENV
		} else {
			env = os.Args[1]
		}
	}

	return env
}

func init() {
	fmt.Println("config init begin")
	// 初始化配置
	InitConfig()
	// 自动刷新配置
	configcenter.AddReloadCallback(InitConfig)
	fmt.Println("config init ok")
	//初始化gas admin
	InitGasAdmin()
}

// InitGasAdmin 初始化 gas 管理员
func InitGasAdmin() {

	userKeyByte, err := ioutil.ReadFile(Conf.GasAdminConfig.PrivateKeyPath)
	if err != nil {
		fmt.Printf("read user private Key file failed, %s\n", err.Error())
		panic(err)
	}

	Conf.GasAdminConfig.PrivateKey, err = asym.PrivateKeyFromPEM(userKeyByte, nil)
	if err != nil {
		fmt.Printf("PrivateKeyFromPEM failed, %s\n", err.Error())
		panic(err)
	}

	pk, err := Conf.GasAdminConfig.PrivateKey.PublicKey().String()
	if err != nil {
		fmt.Printf("get publicKey failed, %s\n", err.Error())
		panic(err)
	}

	Conf.GasAdminConfig.PkByte = []byte(pk)
}

// InitConfig 初始化config
func InitConfig() {
	env := GetConfEnv()
	InitConf(env)
}

// InitConf 初始化config
func InitConf(env string) {
	Conf = new(TomlConfig)
	Conf.LoadConfig(env)
	printLog()
}

func printLog() {
	seelog.Info("======== [Common] ========")
	seelog.Infof("%+v", Conf.Common)
	seelog.Info("======== [MySQL] ========")
	seelog.Infof("%+v", Conf.MySQL)
	seelog.Info("======== [Redis] ========")
	seelog.Infof("%+v", Conf.Redis)
	seelog.Infof("%+v", Conf.FuncInfoMap)
	fmt.Printf("%+v", Conf.FuncInfoMap)
}
