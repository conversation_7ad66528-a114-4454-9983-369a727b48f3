FROM docker.io/alpine:latest

RUN apk add gcompat

#环境变量
ENV ZX_ENV=fttest

#更换apk源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories

#设置时区为上海
RUN apk add tzdata && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

#拷贝静态文件
COPY bin /data/zx/chain_service/bin

#WORKDIR 相当于cd
WORKDIR /data/zx/chain_service/bin

RUN chmod +x chain_service
RUN mkdir /data/zx/chain_service/log
RUN mkdir /data/zx/chain_service/config

#拷贝静态文件
COPY ./config /data/zx/chain_service/config

#CMD 运行以下命令
CMD ["./chain_service", "fttest"]