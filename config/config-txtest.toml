[COMMON]
port = 30505
# 是否开启双向HTTPS认证
open_tls = false
# 如果不启用订阅模式则使用链服务模拟订阅模式。
enable_subscribe = false

[AES]
aes_key = "8650e6eefcc611ff8650e6eefcc611ff"

[Chain]
sdk_config_path = "/data/zx/chain_service/config/sdk_configs/sdk_config_pk_user1.yml"

[GasAdminConfig]
#gas admin privateKey 与其他区分开来
private_key_path = "/data/zx/chain_service/config/crypto-config-pk/gas_admin.key"
org_id = ""
hash_type = "SM3"

[Pulsar]
enable = false
url = "http://pulsar-wdx4xzxz882k.sap-q0ej23o1.tdmq.ap-gz.internal.tencenttdmq.com:8080"
token = "eyJrZXlJZCI6InB1bHNhci13ZHg0eHp4ejg4MmsiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJwdWxzYXItd2R4NHh6eHo4ODJrX2NoYWluLXNlcnZpY2UtdGVzdCJ9.X-X5Kppud0hJT0A4uu0p0r_8WZa6pmCviAfXRIbi3h0"
topic = "pulsar-wdx4xzxz882k/chain-service-test/chainmaker-service"
consumer = "chainmaker_consumer"
operation_timeout = 30
connection_timeout = 30
retry_times = 3

[FuncInfoMap.zxcode.bind]
func_name = "bind"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcode.bind.arg_info_map]
appId = 0
pubKey = 1
signature = 2
[FuncInfoMap.zxcode.update]
func_name = "update"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcode.update.arg_info_map]
appId = 0
pubKey = 1
signature = 2
[FuncInfoMap.zxcode.auth]
func_name = "auth"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcode.auth.arg_info_map]
appId = 0
[FuncInfoMap.zxcode.add]
func_name = "add"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcode.add.arg_info_map]
appId = 0
evId = 1
evHash = 2
extendInfo = 3
timestamp = 4
time = 5
sign = 6
[FuncInfoMap.zxcode.addNoAuth]
func_name = "addNoAuth"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcode.addNoAuth.arg_info_map]
appId = 0
evId = 1
evHash = 2
ext = 3
timestamp = 4
time = 5
signature = 6
[FuncInfoMap.zxcode.dci_claim]
func_name = "dci_claim"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcode.dci_claim.arg_info_map]
dciReq = 0
timestamp = 1
timeStr = 2
[FuncInfoMap.zxcode.get]
func_name = "get"
method_type = 0
gas_limit = 20000000
[FuncInfoMap.zxcode.get.arg_info_map]
key = 0
[FuncInfoMap.zxcode.updateKey]
func_name = "updateKey"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcode.updateKey.arg_info_map]
key = 0
value = 1
[FuncInfoMap.kvcode.kv_add]
func_name = "kv_add"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.kvcode.kv_add.arg_info_map]
appId = 0
kvKey = 1
kvValue = 2
kvKeyHash = 3
kvKeyValueHash = 4
signature = 5
time = 7
[FuncInfoMap.zxkv.kv_save_v1]
func_name = "kv_save_v1"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.zxkv.kv_save_v1.arg_info_map]
pubKey = 0
kvKey = 1
kvValueHash = 2
signature = 3
optId = 4
[FuncInfoMap.nft.apply_point]
func_name = "apply_point"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.apply_point.arg_info_map]
pubPointReq = 0
dynamicMsg = 1
[FuncInfoMap.nft.destroy_point]
func_name = "destroy_point"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.destroy_point.arg_info_map]
destroyPointReq = 0
dynamicMsg = 1
[FuncInfoMap.nft.transfer_point]
func_name = "transfer_point"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.transfer_point.arg_info_map]
transferPointReq = 0
dynamicMsg = 1
[FuncInfoMap.nft.point_balance]
func_name = "point_balance"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.point_balance.arg_info_map]
getPointBalanceReq = 0
[FuncInfoMap.nft.series_claim]
func_name = "series_claim"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.series_claim.arg_info_map]
seriesClaimReq = 0
dynamicMsg = 1
[FuncInfoMap.nft.series_get]
func_name = "series_get"
method_type = 0
gas_limit = 90000000
[FuncInfoMap.nft.series_get.arg_info_map]
seriesGetReq = 0
dynamicMsg = 1
[FuncInfoMap.nft.nft_update_sell]
func_name = "nft_update_sell"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.nft_update_sell.arg_info_map]
nftUpdateSellReq = 0
dynamicMsg = 1
[FuncInfoMap.nft.nft_buy]
func_name = "nft_buy"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.nft_buy.arg_info_map]
nftBuyReq = 0
dynamicMsg = 1
[FuncInfoMap.nft.nft_claim]
func_name = "nft_claim"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.nft_claim.arg_info_map]
nftClaimReq = 0
dynamicMsg = 1
[FuncInfoMap.nft.nft_update_url]
func_name = "nft_update_url"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.nft_update_url.arg_info_map]
nftUpdateUrlReq = 0
dynamicMsg = 1
[FuncInfoMap.nft.nft_get]
func_name = "nft_get"
method_type = 0
gas_limit = 90000000
[FuncInfoMap.nft.nft_get.arg_info_map]
nftKey = 0
dynamicMsg = 1
[FuncInfoMap.nft.nft_transfer]
func_name = "nft_transfer"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.nft_transfer.arg_info_map]
nftTransferReq = 0
dynamicMsg = 1
[FuncInfoMap.nft.nft_transfer_batch]
func_name = "nft_transfer_batch"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nft.nft_transfer_batch.arg_info_map]
nftBatchTransferReq = 0
dynamicMsg = 1
[FuncInfoMap.nftuser.userBind]
func_name = "userBind"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nftuser.userBind.arg_info_map]
userId = 0
signature = 1
pubKey = 2
[FuncInfoMap.nftuser.userBindV2]
func_name = "userBindV2"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nftuser.userBindV2.arg_info_map]
userId = 0
signature = 1
pubKey = 2
[FuncInfoMap.nftuser.userBindV3]
func_name = "userBindV3"
method_type = 1
gas_limit = 90000000
[FuncInfoMap.nftuser.userBindV3.arg_info_map]
keyInfo = 0
signature = 1
pubKey = 2
name = 3
description = 4
userId = 5
[FuncInfoMap.nftuser.queryUser]
func_name = "queryUser"
method_type = 0
gas_limit = 90000000
[FuncInfoMap.nftuser.queryUser.arg_info_map]
address = 0
[FuncInfoMap.nftuser.queryAddressListOfUser]
func_name = "queryAddressListOfUser"
method_type = 0
gas_limit = 90000000
[FuncInfoMap.nftuser.queryAddressListOfUser.arg_info_map]
address = 0
[FuncInfoMap.nftuser.get]
func_name = "get"
method_type = 0
gas_limit = 20000000
[FuncInfoMap.nftuser.get.arg_info_map]
key = 0
[FuncInfoMap.nftuser.updateKey]
func_name = "updateKey"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.nftuser.updateKey.arg_info_map]
key = 0
value = 1
[FuncInfoMap.ev.save_ev]
func_name = "save_ev"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.ev.save_ev.arg_info_map]
pubKey = 0
evId = 1
evHash = 2
extendInfo = 3
timestamp = 4
time = 5
sign = 6
userCcName = 7
userCcMethod = 8
[FuncInfoMap.ev.get]
func_name = "get"
method_type = 0
gas_limit = 20000000
[FuncInfoMap.ev.get.arg_info_map]
key = 0
[FuncInfoMap.ev.updateKey]
func_name = "updateKey"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.ev.updateKey.arg_info_map]
key = 0
value = 1
[FuncInfoMap.zxcc.bind]
func_name = "bind"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcc.bind.arg_info_map]
appId = 0
pubKey = 1
signature = 2
[FuncInfoMap.zxcc.update]
func_name = "update"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcc.update.arg_info_map]
appId = 0
pubKey = 1
signature = 2
[FuncInfoMap.zxcc.auth]
func_name = "auth"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcc.auth.arg_info_map]
appId = 0
[FuncInfoMap.zxcc.add]
func_name = "add"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcc.add.arg_info_map]
appId = 0
evId = 1
evHash = 2
extendInfo = 3
timestamp = 4
time = 5
sign = 6
[FuncInfoMap.zxcc.addNoAuth]
func_name = "addNoAuth"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcc.addNoAuth.arg_info_map]
appId = 0
evId = 1
evHash = 2
ext = 3
timestamp = 4
time = 5
signature = 6
[FuncInfoMap.zxcc.dci_claim]
func_name = "dci_claim"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcc.dci_claim.arg_info_map]
dciReq = 0
timestamp = 1
timeStr = 2
[FuncInfoMap.zxcc.get]
func_name = "get"
method_type = 0
gas_limit = 20000000
[FuncInfoMap.zxcc.get.arg_info_map]
key = 0
[FuncInfoMap.zxcc.updateKey]
func_name = "updateKey"
method_type = 1
gas_limit = 20000000
[FuncInfoMap.zxcc.updateKey.arg_info_map]
key = 0
value = 1
[FuncInfoMap.zxdci.dci_claim]
func_name = "dci_claim"
method_type = 1
gas_limit = 2000000
[FuncInfoMap.zxdci.dci_claim.arg_info_map]
dciReq = 0
timestamp = 1
timeStr = 2