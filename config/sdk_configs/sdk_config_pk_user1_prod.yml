chain_client:
  # 链ID
  chain_id: "chain_dcwyu"

  # 客户端用户交易签名私钥路径
  user_sign_key_file_path: "/data/zx/chain_service/config/crypto-config-pk/client1.sign.key"
  # 签名使用的哈希算法，和节点保持一直
  crypto:
    hash: SM3
  auth_type: public

  nodes:
    - # 节点地址，格式为：IP:端口:连接数
      node_addr: "**************:12301"
      # 节点连接数
      conn_cnt: 10
    - # 节点地址，格式为：IP:端口:连接数
      node_addr: "***************:12301"
      # 节点连接数
      conn_cnt: 10

  archive:
    # 数据归档链外存储相关配置
    type: "mysql"
    dest: "root:123456:localhost:3306"
    secret_key: xxx

  rpc_client:
    max_receive_message_size: 100 # grpc客户端接收消息时，允许单条message大小的最大值(MB)
    max_send_message_size: 100 # grpc客户端发送消息时，允许单条message大小的最大值(MB)
