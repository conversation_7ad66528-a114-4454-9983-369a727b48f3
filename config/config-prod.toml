[COMMON]
port                            = 30505
# 是否开启双向HTTPS认证
open_tls                        = false

[MySQL]
url                             = "172.17.16.25:3306"
user                            = "root"
pwd                             = "K7h#2hsgU5%3E"
db_name                          = "zx"

[AES]
aes_key                         = "8650e6eefcc611ff8650e6eefcc611ff"

[CA]
trusts_path                     = "/data/zx/chain_service/config/cert/trusts"
server_crt                      = "/data/zx/chain_service/config/cert/server.crt"
server_key                      = "/data/zx/chain_service/config/cert/server.key"

[ServiceDomain]
ump = "http://127.0.0.1:30303"
tmw = "http://127.0.0.1:30304"
dlock = "http://127.0.0.1:30310"